import { Injectable, inject } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { LogoutService } from './shared/services/logout.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private toastr = inject(ToastrService);
  private logoutService = inject(LogoutService);

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const excludedUrls = ['/sign-in']; // Add URLs to exclude
    const isExcluded = excludedUrls.some(url => req.url.includes(url));

    if (isExcluded) {
      return next.handle(req); // Skip interception for excluded URLs
    }

    const token = localStorage.getItem('authToken'); // Retrieve token from localStorage

    let clonedRequest = req;
    if (token) {
      clonedRequest = req.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`
        }
      });
    }

    return next.handle(clonedRequest).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('HTTP Error:', error);

        // Only show interceptor errors for critical cases
        switch (error.status) {
          case 401:
            this.logoutService.handle401Error();
            break;
          case 403:
            this.toastr.error('You do not have permission to access this resource.');
            break;
          case 500:
            // Only show 500 errors if it's not from promotion APIs
            if (!this.isPromotionAPI(error.url)) {
              this.toastr.error('An unexpected error occurred. Please try again later.');
            }
            break;
          default:
            // Don't show generic errors for promotion APIs - let component handle them
            if (!this.isPromotionAPI(error.url)) {
              this.toastr.error('An error occurred. Please try again.');
            }
        }
        return throwError(() => error);
      })
    );
  }



  /**
   * Check if the API call is related to promotions
   */
  private isPromotionAPI(url: string | null): boolean {
    if (!url) return false;

    const promotionEndpoints = [
      'approve-promotion',
      'reject-promotion',
      'add-promotions',
      'update-promotion',
      'get-all-promotions'
    ];

    return promotionEndpoints.some(endpoint => url.includes(endpoint));
  }
}
